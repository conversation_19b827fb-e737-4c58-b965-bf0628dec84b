package store

import "fmt"

type Contact struct {
	ID    int
	Name  string
	Email string
}

type ContactStore struct {
	Contacts    []Contact
	IDGenerator func() int
}

// Create a closure that returns a function with persistent state
func counter() func() int {
	ct := 0
	return func() int {
		ct++
		return ct
	}
}

// Create the counter closure once, outside the handler

func NewContactStore() *ContactStore {
	return &ContactStore{
		Contacts:    make([]Contact, 0),
		IDGenerator: counter(),
	}
}

func (u *ContactStore) DeleteContact(id int) error {

	pos := -1
	for _, contact := range u.GetContacts() {
		if contact.ID == id {
			pos = id
		}
	}

	if pos > -1 {
		u.Contacts = append(u.Contacts[:pos], u.Contacts[pos+1:]...)
	}

	return nil
}
func (u *ContactStore) AddContact(name, email string) error {

	if u.AlreadyExists(email) {
		return fmt.Errorf("contact already exists")
	}

	id := u.IDGenerator()

	fmt.Printf("Debug !!!!! %v", id)
	u.Contacts = append(u.Contacts, Contact{id, name, email})
	return nil
}

func (u *ContactStore) AlreadyExists(email string) bool {
	for _, contact := range u.GetContacts() {
		if contact.Email == email {
			return true
		}
	}
	return false
}
func (u *ContactStore) GetContacts() []Contact {
	return u.Contacts
}

func (u *ContactStore) ClearData() {
	u.Contacts = make([]Contact, 0)
}

func (u *ContactStore) InitData() {
	u.AddContact("bob", "<EMAIL>")
	u.AddContact("jane", "<EMAIL>")
}
