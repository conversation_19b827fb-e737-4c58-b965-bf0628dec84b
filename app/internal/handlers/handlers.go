package handlers

import (
	"github.com/labstack/echo/v4"
	"github.com/nickabs/demo/internal/store"
	"github.com/nickabs/demo/internal/templates"
)

func RegisterPage(c echo.Context) error {
	component := templates.RegisterPage()
	return component.Render(c.Request().Context(), c.Response().Writer)
}

func LoadContacts(c echo.Context) error {
	userStore := c.Get("userStore").(*store.UserStore)
	return templates.RenderStoredContacts(userStore).Render(c.Request().Context(), c.Response().Writer)
}

func Clear(c echo.Context) error {
	userStore := c.Get("userStore").(*store.UserStore)

	userStore.ClearData()

	c.Response().Write([]byte(`<div class="contacts"> no contacts available</div>`))

	return nil
}
func Register(c echo.Context) error {
	userStore := c.Get("userStore").(*store.UserStore)
	req := c.Request()
	res := c.Response()
	ctx := req.Context()

	name := req.FormValue("name")
	email := req.FormValue("email")

	if err := userStore.AddUser(name, email); err != nil {
		res.Write([]byte(`<div id="error"> error: user already exists`))
	}

	templates.RenderStoredContacts(userStore).Render(ctx, res.Writer)
	return nil
}
