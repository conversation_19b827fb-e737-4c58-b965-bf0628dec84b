package handlers

import (
	"strconv"

	"github.com/labstack/echo/v4"
	"github.com/nickabs/demo/internal/store"
	"github.com/nickabs/demo/internal/templates"
)

func RegisterPage(c echo.Context) error {
	component := templates.RegisterPage()
	return component.Render(c.Request().Context(), c.Response().Writer)
}

func LoadContacts(c echo.Context) error {
	contactStore := c.Get("contactStore").(*store.ContactStore)
	return templates.RenderStoredContacts(contactStore).Render(c.Request().Context(), c.Response().Writer)
}

func DeleteContact(c echo.Context) error {
	contactStore := c.Get("contactStore").(*store.ContactStore)
	//contactId := c.Param("contactId").(int)
	id, _ := strconv.Atoi(c.Param("contactID"))

	contactStore.DeleteContact(id)

	c.Response().Write([]byte(""))
	return nil
}

func Clear(c echo.Context) error {
	contactStore := c.Get("contactStore").(*store.ContactStore)

	contactStore.ClearData()

	c.Response().Write([]byte(`<div class="contacts"> no contacts available</div>`))

	return nil
}
func Register(c echo.Context) error {
	contactStore := c.Get("contactStore").(*store.ContactStore)
	req := c.Request()
	res := c.Response()
	ctx := req.Context()

	name := req.FormValue("name")
	email := req.FormValue("email")

	if err := contactStore.AddContact(name, email); err != nil {
		res.Write([]byte(`<div id="error"> error: contact already exists`))
	}

	templates.RenderStoredContacts(contactStore).Render(ctx, res.Writer)
	return nil
}
