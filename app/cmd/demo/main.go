package main

import (
	"github.com/labstack/echo/v4"
	"github.com/labstack/echo/v4/middleware"
	"github.com/nickabs/demo/internal/handlers"
	"github.com/nickabs/demo/internal/store"
)

func main() {
	e := echo.New()

	// Create user store
	userStore := store.NewUserStore()

	userStore.InitData()

	// Middleware to inject user store into context
	e.Use(func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			c.Set("userStore", userStore)
			return next(c)
		}
	})

	e.Use(middleware.Logger())

	e.Static("static", "assets")
	e.GET("/register", handlers.RegisterPage)
	e.GET("/contacts", handlers.LoadContacts)
	e.POST("/register", handlers.Register)
	e.POST("/clear", handlers.Clear)
	//e.DELETE("/contacts/")

	e.Logger.Fatal(e.Start(":8181"))
}
